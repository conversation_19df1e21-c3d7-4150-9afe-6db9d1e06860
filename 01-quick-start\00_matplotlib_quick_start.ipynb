# 导入必要的库
import matplotlib as mlp        # 导入matplotlib库，用于输出版本号
import matplotlib.pyplot as plt # matplotlib的绘图接口，plt是约定俗成的别名
import numpy as np              # 数值计算库，用于生成示例数据
import pandas as pd             # 数据处理库，用于处理表格数据

# 设置图表显示样式
plt.style.use('default')  # 使用默认样式，确保图表外观一致

# 设置中文字体支持（解决中文显示为方块的问题）
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
# 注意设置默认样式应在设置中文字体之前，否则可能会影响字体渲染


# 在Jupyter中内联显示图表（让图表直接显示在notebook中）
%matplotlib inline

print("✅ 环境设置完成！")
print("📊 matplotlib版本:", mlp.__version__)
print("🐍 numpy版本:", np.__version__)
print("🐼 pandas版本:", pd.__version__)

# 准备数据（就像Excel中的两列数据）
x = [1, 2, 3, 4, 5]    # X轴的值：可以理解为时间点或者类别
y = [2, 5, 3, 8, 7]    # Y轴的值：对应的数据值

# 创建图表
plt.figure(figsize=(8, 6))  # 创建一个8×6英寸的画布
plt.plot(x, y)             # 绘制线图，连接所有数据点
plt.show()                 # 显示图表

print("🎉 恭喜！你已经创建了第一个matplotlib图表！")

# 同样的数据
x = [1, 2, 3, 4, 5]
y = [2, 5, 3, 8, 7]

# 创建美化的图表
plt.figure(figsize=(10, 6))  # 稍微大一点的画布

# 绘制线条，添加样式
plt.plot(x, y, 
         color='blue',          # 颜色：蓝色
         marker='o',            # 标记点：圆形
         linewidth=2,           # 线宽：2像素
         markersize=8,          # 标记大小：8像素
         label='数据系列1')      # 图例标签

# 添加标题和标签
plt.title('我的第一个美化图表', fontsize=16, fontweight='bold')
plt.xlabel('X轴标签', fontsize=12)
plt.ylabel('Y轴标签', fontsize=12)

# 添加网格（让数据更容易读取）
plt.grid(
    True,                     # 是否显示网格
    linestyle='--',           # 网格线样式：虚线
    alpha=0.7                 # alpha控制透明度
)

# 添加图例
plt.legend()

# 显示图表
plt.show()

print("✨ 图表变得更美观了！")

# 准备示例数据
np.random.seed(42)  # 设置随机种子，确保结果可重复
x = np.linspace(0, 10, 20)  # 生成0到10之间的20个等间距点
y1 = x + np.random.normal(0, 1, 20)  # 线性趋势+随机噪声
y2 = x**2 / 10 + np.random.normal(0, 2, 20)  # 二次趋势+随机噪声
categories = ['A', 'B', 'C', 'D', 'E']  # 分类标签
values = [23, 45, 56, 78, 32]  # 对应的数值
data_hist = np.random.normal(100, 15, 200)  # 正态分布数据

# 创建子图（2行4列的图表网格）
fig, axes = plt.subplots(2, 4, figsize=(16, 8))
fig.suptitle('Matplotlib常见图表类型预览', fontsize=16, fontweight='bold')

# 1. 线图 - 显示趋势变化
axes[0,0].plot(x, y1, 'b-o', markersize=4)
axes[0,0].set_title('线图\n显示趋势变化')
axes[0,0].grid(True, alpha=0.3)

# 2. 散点图 - 显示相关性
axes[0,1].scatter(y1, y2, alpha=0.6, color='red')
axes[0,1].set_title('散点图\n显示相关性')
axes[0,1].grid(True, alpha=0.3)

# 3. 柱状图 - 比较不同类别
axes[0,2].bar(categories, values, color='skyblue')
axes[0,2].set_title('柱状图\n比较不同类别')

# 4. 饼图 - 显示占比关系
axes[0,3].pie(values, labels=categories, autopct='%1.1f%%')
axes[0,3].set_title('饼图\n显示占比关系')

# 5. 直方图 - 显示数据分布
axes[1,0].hist(data_hist, bins=20, alpha=0.7, color='green')
axes[1,0].set_title('直方图\n显示数据分布')

# 6. 箱线图 - 显示数据分布特征
axes[1,1].boxplot([data_hist])
axes[1,1].set_title('箱线图\n显示数据分布特征')

# 7. 热力图 - 显示二维数据
heatmap_data = np.random.rand(5, 5)
im = axes[1,2].imshow(heatmap_data, cmap='viridis')
axes[1,2].set_title('热力图\n显示二维数据')

# 8. 多条线图 - 比较多个趋势
axes[1,3].plot(x, y1, label='系列1', linewidth=2)
axes[1,3].plot(x, y2, label='系列2', linewidth=2)
axes[1,3].legend()
axes[1,3].set_title('多系列线图\n比较多个趋势')
axes[1,3].grid(True, alpha=0.3)

plt.tight_layout()  # 自动调整子图间距
plt.show()

print("🌟 这些就是你将要学会的图表类型！")

# 方式一：pyplot风格（plt.xxx）- 简单直接
print("🎨 方式一：pyplot风格")

# 准备数据
x = [1, 2, 3, 4, 5]
y = [2, 5, 3, 8, 7]

# 使用pyplot风格绘图
plt.figure(figsize=(8, 5))  # 创建图形
plt.plot(x, y, 'b-o')       # 绘制线图
plt.title('pyplot风格示例') # 添加标题
plt.xlabel('X轴')           # X轴标签
plt.ylabel('Y轴')           # Y轴标签
plt.grid(True, alpha=0.3)   # 添加网格
plt.show()                  # 显示图表

print("✅ pyplot风格：简单直接，适合快速绘图")

# 方式二：面向对象风格（ax.xxx）- 更灵活更强大
print("🎯 方式二：面向对象风格")

# 同样的数据
x = [1, 2, 3, 4, 5]
y = [2, 5, 3, 8, 7]

# 使用面向对象风格绘图
fig, ax = plt.subplots(figsize=(8, 5))  # 创建图形和坐标轴对象
ax.plot(x, y, 'r-s')                    # 绘制线图
ax.set_title('面向对象风格示例')         # 添加标题
ax.set_xlabel('X轴')                    # X轴标签
ax.set_ylabel('Y轴')                    # Y轴标签
ax.grid(True, alpha=0.3)                # 添加网格
plt.show()                              # 显示图表

print("✅ 面向对象风格：更灵活，适合复杂图表和专业项目")

# 🌟 实战演示：为什么推荐面向对象风格？
print("🎨 演示：面向对象风格处理多子图的优势")

# 准备两组不同的数据
x = [1, 2, 3, 4, 5]
sales = [100, 120, 150, 140, 160]      # 销售数据
temperature = [22, 25, 28, 24, 26]     # 温度数据

# 使用面向对象风格创建多子图
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

# 在第一个子图中绘制销售数据
ax1.plot(x, sales, 'b-o', linewidth=2, markersize=6)
ax1.set_title('销售趋势')
ax1.set_xlabel('月份')
ax1.set_ylabel('销售额（万元）')
ax1.grid(True, alpha=0.3)

# 在第二个子图中绘制温度数据
ax2.plot(x, temperature, 'r-s', linewidth=2, markersize=6)
ax2.set_title('温度变化')
ax2.set_xlabel('月份')
ax2.set_ylabel('温度（°C）')
ax2.grid(True, alpha=0.3)

plt.tight_layout()  # 自动调整布局
plt.show()

print("✅ 看到了吗？面向对象风格让多子图变得非常简单！")
print("💡 用pyplot风格实现同样效果会复杂很多")

# 对比：如果用pyplot风格实现上面的多子图，代码会像这样：
print("\n📝 如果用pyplot风格，代码会是这样：")
print("plt.subplot(1, 2, 1)  # 选择第一个子图")
print("plt.plot(x, sales, 'b-o')")
print("plt.title('销售趋势')")
print("plt.subplot(1, 2, 2)  # 选择第二个子图")
print("plt.plot(x, temperature, 'r-s')")
print("plt.title('温度变化')")
print("# 容易搞混是在哪个子图上操作！")

# 📊 matplotlib标准工作流程示例

# 步骤1：准备数据
months = ['1月', '2月', '3月', '4月', '5月', '6月']
sales = [120, 135, 142, 158, 163, 171]  # 销售额（万元）
profit = [23, 28, 31, 35, 38, 42]       # 利润（万元）

print("步骤1 ✅：数据准备完成")

# 步骤2：创建图形和坐标轴
fig, ax = plt.subplots(figsize=(10, 6))
print("步骤2 ✅：创建画布和坐标轴")

# 步骤3：绘制数据
line1 = ax.plot(months, sales, 'b-o', linewidth=2, markersize=6, label='销售额')
line2 = ax.plot(months, profit, 'r-s', linewidth=2, markersize=6, label='利润')
print("步骤3 ✅：绘制数据")

# 步骤4：添加装饰元素
ax.set_title('公司上半年销售情况', fontsize=14, fontweight='bold', pad=20)
ax.set_xlabel('月份', fontsize=12)
ax.set_ylabel('金额 (万元)', fontsize=12)
ax.legend(loc='upper left')
ax.grid(True, alpha=0.3)
print("步骤4 ✅：添加标题、标签、图例等")

# 步骤5：调整和美化
ax.set_ylim(0, max(max(sales), max(profit)) * 1.1)  # 设置Y轴范围
plt.tight_layout()  # 调整布局
print("步骤5 ✅：调整布局和美化")

# 步骤6：显示或保存
plt.show()
print("步骤6 ✅：显示图表")

# 可选：保存图表
# plt.savefig('sales_chart.png', dpi=300, bbox_inches='tight')
# print("💾 图表已保存为 sales_chart.png")

print("\n🎯 标准工作流程总结：")
print("1️⃣ 准备数据 → 2️⃣ 创建画布 → 3️⃣ 绘制图形")
print("4️⃣ 添加装饰 → 5️⃣ 调整美化 → 6️⃣ 显示保存")

# ❌ 错误示例：没有设置中文字体
# plt.rcParams没有正确设置时，中文会显示为方块

# ✅ 正确解决方案：
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 测试中文显示
plt.figure(figsize=(8, 5))
plt.plot([1, 2, 3], [1, 4, 2])
plt.title('中文标题测试')
plt.xlabel('X轴（中文）')
plt.ylabel('Y轴（中文）')
plt.show()

print("✅ 中文显示正常！")

# ❌ 常见错误：忘记调用 plt.show()
plt.figure(figsize=(6, 4))
plt.plot([1, 2, 3], [1, 4, 2])
plt.title('这个图表可能不会显示')
# 缺少 plt.show()

print("⚠️ 上面的图表可能没有显示")

# ✅ 正确做法：记得添加 plt.show()
plt.figure(figsize=(6, 4))
plt.plot([1, 2, 3], [1, 4, 2])
plt.title('这个图表会正常显示')
plt.show()  # 重要：显示图表

print("✅ 图表显示正常！")
print("💡 提示：在Jupyter中，有时可以省略plt.show()，但建议总是添加")

# 演示不同大小的图表
data_x = [1, 2, 3, 4]
data_y = [1, 4, 2, 3]

# 太小的图表（4×3英寸）
plt.figure(figsize=(4, 3))
plt.plot(data_x, data_y, 'o-')
plt.title('太小的图表（4×3）')
plt.show()

# 合适大小的图表（8×6英寸）
plt.figure(figsize=(8, 6))
plt.plot(data_x, data_y, 'o-')
plt.title('合适大小的图表（8×6）')
plt.show()

# 太大的图表（15×10英寸）
plt.figure(figsize=(15, 10))
plt.plot(data_x, data_y, 'o-')
plt.title('太大的图表（15×10）')
plt.show()

print("📏 图表大小建议：")
print("• 一般用途：figsize=(8, 6) 或 (10, 6)")
print("• 宽屏显示：figsize=(12, 6) 或 (14, 8)")
print("• 正方形图：figsize=(8, 8)")
print("• 小图预览：figsize=(6, 4)")

# 创建一个示例图表
plt.figure(figsize=(8, 6))
x = [1, 2, 3, 4, 5]
y = [2, 5, 3, 8, 7]
plt.plot(x, y, 'bo-', linewidth=2, markersize=8)
plt.title('保存图片示例', fontsize=14)
plt.xlabel('X轴')
plt.ylabel('Y轴')
plt.grid(True, alpha=0.3)

# 保存为不同格式
# plt.savefig('my_chart.png', dpi=300, bbox_inches='tight')  # PNG格式，高分辨率
# plt.savefig('my_chart.pdf', bbox_inches='tight')           # PDF格式，矢量图
# plt.savefig('my_chart.jpg', dpi=200, bbox_inches='tight')  # JPG格式

plt.show()

print("💾 保存图片的重要参数：")
print("• dpi=300：设置分辨率（300是印刷级质量）")
print("• bbox_inches='tight'：自动裁剪空白边缘")
print("• format='png'：指定文件格式")
print("\n📁 常用格式：")
print("• PNG：网页显示，支持透明背景")
print("• PDF：打印出版，矢量格式不失真")
print("• JPG：照片格式，文件较小")
print("• SVG：网页矢量图，可在浏览器中缩放")

# 数据：一周的学习时间记录
days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
study_hours = [2, 3, 1.5, 4, 3.5, 5, 2.5]

# TODO: 请你完成以下任务
# 1. 创建一个线图显示学习时间变化
# 2. 添加合适的标题和轴标签
# 3. 使用红色线条和圆形标记
# 4. 添加网格
# 5. 设置图表大小为10x6

# 在这里写你的代码：






print("\n🎯 检查清单：")
print("- [ ]: 图表大小是10×6")
print("- [ ]: 使用了红色线条")
print("- [ ]: 数据点有圆形标记")
print("- [ ]: 有合适的标题")
print("- [ ]: X轴和Y轴都有标签")
print("- [ ]: 显示了网格")

# 练习1参考答案
days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
study_hours = [2, 3, 1.5, 4, 3.5, 5, 2.5]

# 创建图表
plt.figure(figsize=(10, 6))  # 设置图表大小

# 绘制红色线条和圆形标记
plt.plot(days, study_hours, 
         color='red',     # 红色线条
         marker='o',      # 圆形标记
         linewidth=2,     # 线宽
         markersize=8)    # 标记大小

# 添加标题和标签
plt.title('一周学习时间记录', fontsize=14, fontweight='bold')
plt.xlabel('星期', fontsize=12)
plt.ylabel('学习时间（小时）', fontsize=12)

# 添加网格
plt.grid(True, alpha=0.3)

# 显示图表
plt.show()

print("✅ 练习1完成！你学会了创建基本的线图")

# 数据：公司四个季度的销售数据
quarters = ['Q1', 'Q2', 'Q3', 'Q4']
product_a = [150, 180, 220, 200]  # 产品A销量
product_b = [120, 140, 160, 180]  # 产品B销量
product_c = [80, 90, 110, 130]    # 产品C销量

# TODO: 请你完成以下任务
# 1. 创建一个图表显示三个产品的销量变化
# 2. 使用不同颜色和标记区分三个产品
# 3. 添加图例说明每条线代表什么
# 4. 添加合适的标题和轴标签
# 5. 设置Y轴范围从0开始
# 6. 添加网格辅助读数

# 在这里写你的代码：






print("\n🎯 检查清单：")
print("- [ ]: 绘制了三条不同颜色的线")
print("- [ ]: 每条线都有不同的标记")
print("- [ ]: 有图例说明")
print("- [ ]: Y轴从0开始")
print("- [ ]: 标题和轴标签清晰")
print("- [ ]: 有网格辅助")

# 练习2参考答案
quarters = ['Q1', 'Q2', 'Q3', 'Q4']
product_a = [150, 180, 220, 200]
product_b = [120, 140, 160, 180]
product_c = [80, 90, 110, 130]

# 创建图表
plt.figure(figsize=(10, 6))

# 绘制三个产品的数据
plt.plot(quarters, product_a, 'b-o', linewidth=2, markersize=8, label='产品A')
plt.plot(quarters, product_b, 'r-s', linewidth=2, markersize=8, label='产品B')
plt.plot(quarters, product_c, 'g-^', linewidth=2, markersize=8, label='产品C')

# 添加标题和标签
plt.title('公司四季度产品销量对比', fontsize=14, fontweight='bold')
plt.xlabel('季度', fontsize=12)
plt.ylabel('销量（万件）', fontsize=12)

# 设置Y轴范围从0开始
plt.ylim(0, max(max(product_a), max(product_b), max(product_c)) * 1.1)

# 添加图例和网格
plt.legend(loc='upper left')
plt.grid(True, alpha=0.3)

# 显示图表
plt.show()

print("✅ 练习2完成！你学会了创建多系列数据图表")
print("🎓 现在你可以处理更复杂的数据可视化任务了！")